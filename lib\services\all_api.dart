import 'package:dio/dio.dart';
import 'package:assignment/model/category_model.dart';
import 'package:assignment/model/model_product.dart';

class ApiService {
  final Dio _dio = Dio(
    BaseOptions(
      baseUrl: 'http://213.210.36.38:8001',
    ),
  );

  /// Fetch all categories
  Future<List<Categories>> fetchAllCategories({
    required int vendorId,
    required String language,
  }) async {
    try {
      final response = await _dio.get(
        '/pos/allCategory/',
        queryParameters: {'vendorId': vendorId, 'language': language},
      );

      if (response.statusCode == 200) {
        // Handle nested categories
        final data = response.data;
        final List<dynamic> categoriesList = data['categories'];
        return categoriesList.map((json) => Categories.fromJson(json)).toList();
      } else {
        throw Exception(
            'Failed to fetch categories: ${response.statusMessage}');
      }
    } on DioError catch (dioError) {
      print('DioError: ${dioError.message}');
      throw Exception('Network Error: ${dioError.message}');
    } catch (error) {
      print('Error: $error');
      throw Exception('Unexpected Error: $error');
    }
  }

  /// Fetch products by category
  Future<List<ProductDetails>> fetchProductsByCategory({
    required int categoryId,
    required int vendorId,
    required String language,
  }) async {
    try {
      // Sending GET request with query parameters
      final response = await _dio.get(
        '/pos/productByCategory/$categoryId/',
        // URL part with categoryId
        queryParameters: {
          'vendorId': vendorId,
          'language': language,
        },
      );

      if (response.statusCode == 200) {
        final data = response.data;

        // Ensure that 'products' exists and is a List
        if (data != null && data['products'] is List) {
          final List<dynamic> productsList = data['products'];
          // Convert each item into a ProductDetails object
          return productsList
              .map((json) => ProductDetails.fromJson(json))
              .toList();
        } else {
          throw Exception(
              'Unexpected response format: Missing or invalid "products" key');
        }
      } else {
        throw Exception('Failed to fetch products: ${response.statusMessage}');
      }
    } on DioError catch (dioError) {
      // Handle Dio-specific errors (e.g., network issues, invalid responses)
      print('DioError: ${dioError.message}');
      throw Exception('Network Error: ${dioError.message}');
    } catch (error) {
      // Handle any other unexpected errors
      print('Error: $error');
      throw Exception('Unexpected Error: $error');
    }
  }
}
