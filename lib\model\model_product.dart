class Product {
  String? message;
  Products? products;

  Product({this.message, this.products});

  Product.fromJson(Map<String, dynamic> json) {
    message = json['message'];
    products = json['products'] != null ? Products.fromJson(json['products']) : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['message'] = message;
    if (products != null) {
      data['products'] = products!.toJson();
    }
    return data;
  }
}

class Products {
  List<ProductDetails>? productDetails;

  Products({this.productDetails});

  Products.fromJson(Map<String, dynamic> json) {
    if (json['64'] != null) {
      productDetails = <ProductDetails>[];
      json['64'].forEach((v) {
        productDetails!.add(ProductDetails.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    if (productDetails != null) {
      data['64'] = productDetails!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class ProductDetails {
  int? categoryId;
  String? categoryName;
  int? productId;
  String? plu;
  String? name;
  String? description;
  double? cost;
  String? tag;
  String? imagePath;
  List<String>? images;
  bool? isTaxable;
  String? type;
 // List<dynamic>? variant;
  int? quantity;
  bool? active;
 // List<dynamic>? modifiersGroup;

  ProductDetails({
    this.categoryId,
    this.categoryName,
    this.productId,
    this.plu,
    this.name,
    this.description,
    this.cost,
    this.tag,
    this.imagePath,
    this.images,
    this.isTaxable,
    this.type,
   // this.variant,
    this.quantity,
    this.active,
    //this.modifiersGroup,
  });

  ProductDetails.fromJson(Map<String, dynamic> json) {
    categoryId = json['categoryId'];
    categoryName = json['categoryName'];
    productId = json['productId'];
    plu = json['plu'];
    name = json['name'];
    description = json['description'];
    cost = json['cost']?.toDouble();
    tag = json['tag'];
    imagePath = json['imagePath'];
    images = json['images']?.cast<String>();
    isTaxable = json['isTaxable'];
    type = json['type'];
   // variant = json['variant'];
    quantity = json['quantity'];
    active = json['active'];
   // modifiersGroup = json['modifiersGroup'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['categoryId'] = categoryId;
    data['categoryName'] = categoryName;
    data['productId'] = productId;
    data['plu'] = plu;
    data['name'] = name;
    data['description'] = description;
    data['cost'] = cost;
    data['tag'] = tag;
    data['imagePath'] = imagePath;
    data['images'] = images;
    data['isTaxable'] = isTaxable;
    data['type'] = type;
    //data['variant'] = variant;
    data['quantity'] = quantity;
    data['active'] = active;
    //data['modifiersGroup'] = modifiersGroup;
    return data;
  }
}
